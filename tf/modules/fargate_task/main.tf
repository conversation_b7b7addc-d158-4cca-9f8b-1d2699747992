locals {
  task_name               = "${var.prefix}-task"
  ecs_execution_role_name = "${var.prefix}-execution-role"
  ecs_task_role_name      = "${var.prefix}-task-role"
}

resource "aws_ecr_repository" "default" {
  image_tag_mutability = "IMMUTABLE"
  name                 = var.ecr_repository_name
}

module "execution_role" {
  source = "./modules/execution-role"

  ecs_task_execution_role_name = local.ecs_execution_role_name
  secrets_enabled              = var.secrets_enabled
  secrets_arns                 = var.secrets_arns
  task_name                    = local.task_name
  aws_region                   = var.aws_region
}

module "task_role" {
  source = "./modules/task-role"

  task_name                  = local.task_name
  aws_region                 = var.aws_region
  ecs_dynamo_policy_resource = var.ecs_dynamo_policy_resource
  ecs_task_role_name         = local.ecs_task_role_name
  dynamo_access_enabled      = var.dynamo_access_enabled
  lambda_access_enabled      = var.lambda_access_enabled
  ecs_lambda_policy_resource = var.ecs_lambda_policy_resource
  sqs_access_enabled         = var.sqs_access_enabled
  ecs_sqs_policy_resource    = var.ecs_sqs_policy_resource
  ecs_sns_policy_resource    = var.ecs_sns_policy_resource
  sns_access_enabled         = var.sns_access_enabled
  s3_bucket_arns             = var.s3_bucket_arns
  s3_read_objects            = var.s3_read_objects

  textract_enabled         = var.textract_enabled
  send_email               = var.send_email
  user_pool_arn_enabled    = var.user_pool_arn_enabled
  user_pool_arn            = var.user_pool_arn
  user_pool_name           = var.user_pool_name
  user_pool_password_policy = var.user_pool_password_policy
  user_pool_client_name    = var.user_pool_client_name
  kms_enabled              = var.kms_enabled
  kms_key_arns             = var.kms_key_arns
}

resource "aws_cloudwatch_log_group" "default" {
  name = "/ecs/${local.task_name}"
  log_group_class   = "INFREQUENT_ACCESS"
  retention_in_days = 0
}

resource "aws_cloudwatch_log_group" "default-dd" {
  log_group_class   = "STANDARD"
  name              = "/ecs/${local.task_name}-dd"
  retention_in_days = 7
}

resource "aws_cloudwatch_log_group" "limited" {
  name              = "/logs/${local.task_name}"
  retention_in_days = 180
}

module "fargate_task_image_version" {
  source         = "../image_version"
  container_name = var.container_name
  cluster_name   = var.cluster_name
  service_name   = var.service_name
  task_name      = local.task_name
  ecr_repository_name = aws_ecr_repository.default.name
}

resource "aws_ecs_task_definition" "app" {
  family                   = local.task_name
  execution_role_arn       = module.execution_role.arn
  task_role_arn            = module.task_role.arn
  network_mode             = var.use_fargate ? "awsvpc" : "bridge"
  requires_compatibilities = var.use_fargate ? ["FARGATE"] : ["EC2"]
  cpu                      = var.fargate_cpu
  memory                   = var.fargate_memory
  track_latest = true

  container_definitions    = templatefile(var.task_definition, merge({
    app_image = "${aws_ecr_repository.default.repository_url}:${module.fargate_task_image_version.image_tag}"
    app_port                  = var.app_port
    container_name            = var.container_name
    service_name              = var.service_name
    secret_arns               = var.secrets_arns
    environment               = var.environment
    account_id                = var.account_id
    task_name                 = local.task_name
    app_environments          = var.app_environments
    secrets_map               = var.secrets_map
  },var.other_container_definitions))
  tags = var.tags

  dynamic "ephemeral_storage" {
    for_each = var.ephemeral_storage > 0 ? [1] : []
    content {
      size_in_gib = var.ephemeral_storage
    }
  }

  lifecycle {
    #ignore_changes = [container_definitions, tags]
    ignore_changes = [tags]
  }
}