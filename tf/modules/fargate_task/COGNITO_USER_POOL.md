# Cognito User Pool Integration

This module now supports creating a Cognito User Pool when the `user_pool_arn_enabled` flag is set to `true`.

## Usage

### Basic Usage

To enable Cognito User Pool creation, set the `user_pool_arn_enabled` variable to `true`:

```hcl
module "fargate_task" {
  source = "./modules/fargate_task"
  
  # ... other variables ...
  
  user_pool_arn_enabled = true
}
```

### Advanced Configuration

You can customize the User Pool configuration:

```hcl
module "fargate_task" {
  source = "./modules/fargate_task"
  
  # ... other variables ...
  
  user_pool_arn_enabled = true
  user_pool_name        = "my-app-user-pool"
  user_pool_client_name = "my-app-client"
  
  user_pool_password_policy = {
    minimum_length                   = 12
    require_lowercase                = true
    require_numbers                  = true
    require_symbols                  = true
    require_uppercase                = true
    temporary_password_validity_days = 7
  }
}
```

### Using External User Pool

If you prefer to use an existing User Pool, you can provide the ARN:

```hcl
module "fargate_task" {
  source = "./modules/fargate_task"
  
  # ... other variables ...
  
  user_pool_arn_enabled = true
  user_pool_arn         = "arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_XXXXXXXXX"
}
```

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| user_pool_arn_enabled | Enable Cognito User Pool functionality | `bool` | `false` | no |
| user_pool_arn | ARN of existing User Pool (if not creating new one) | `string` | `""` | no |
| user_pool_name | Name for the new User Pool | `string` | `""` | no |
| user_pool_client_name | Name for the User Pool Client | `string` | `""` | no |
| user_pool_password_policy | Password policy configuration | `object` | See below | no |

### Default Password Policy

```hcl
{
  minimum_length                   = 8
  require_lowercase                = true
  require_numbers                  = true
  require_symbols                  = false
  require_uppercase                = true
  temporary_password_validity_days = 7
}
```

## Outputs

When `user_pool_arn_enabled` is `true`, the following outputs are available:

| Name | Description |
|------|-------------|
| user_pool_id | ID of the Cognito User Pool |
| user_pool_arn | ARN of the Cognito User Pool |
| user_pool_client_id | ID of the User Pool Client |
| user_pool_client_secret | Secret of the User Pool Client (sensitive) |

## Features

- **Automatic User Pool Creation**: Creates a new Cognito User Pool when enabled
- **Configurable Password Policy**: Customize password requirements
- **Email Verification**: Auto-verified email attributes
- **Admin-Only User Creation**: Only administrators can create users
- **IAM Integration**: Automatically creates IAM policies for Cognito operations
- **Flexible Configuration**: Use either new or existing User Pool

## IAM Permissions

The module automatically grants the following Cognito permissions to the ECS task role:

- `cognito-idp:AdminAddUserToGroup`
- `cognito-idp:AdminRemoveUserFromGroup`
- `cognito-idp:AdminCreateUser`
- `cognito-idp:AdminGetUser`
- `cognito-idp:AdminResetUserPassword`
- `cognito-idp:AdminSetUserMFAPreference`
- `cognito-idp:AdminSetUserPassword`
- `cognito-idp:AdminSetUserSettings`
- `cognito-idp:AdminUpdateUserAttributes`
- `cognito-idp:AdminUserGlobalSignOut`
- `cognito-idp:AdminDisableUser`
- `cognito-idp:AdminDeleteUserAttributes`
