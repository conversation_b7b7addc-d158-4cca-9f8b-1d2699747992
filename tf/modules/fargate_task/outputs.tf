output "arn" {
  value = aws_ecs_task_definition.app.arn
}

output "task_role_arn" {
  value = module.task_role.arn
}

output "default_log_group_name" { value = aws_cloudwatch_log_group.default.name }

output "limited_log_group_name" { value = aws_cloudwatch_log_group.limited.name }

output "user_pool_id" {
  description = "ID of the Cognito User Pool (only available when user_pool_arn_enabled is true)"
  value       = module.task_role.user_pool_id
}

output "user_pool_arn" {
  description = "ARN of the Cognito User Pool (only available when user_pool_arn_enabled is true)"
  value       = module.task_role.user_pool_arn
}

output "user_pool_client_id" {
  description = "ID of the Cognito User Pool Client (only available when user_pool_arn_enabled is true)"
  value       = module.task_role.user_pool_client_id
}

output "user_pool_client_secret" {
  description = "Secret of the Cognito User Pool Client (only available when user_pool_arn_enabled is true)"
  value       = module.task_role.user_pool_client_secret
  sensitive   = true
}