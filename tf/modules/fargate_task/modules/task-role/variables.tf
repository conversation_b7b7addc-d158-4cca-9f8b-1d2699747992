variable "dynamo_access_enabled" {
  default     = false
  description = "Dynamo access"
}

variable "ecs_dynamo_policy_resource" {
  description = " ECS dynamo policy resources"
}

variable "ecs_task_role_name" {
  description = "ECS task role name"
}

variable "ecs_lambda_policy_resource" {
  description = "ECS policy resources for lambda integration"
  default     = []
}

variable "lambda_access_enabled" {
  default = false
}

variable "task_name" {
  description = "ECS task name"
}

variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "ecs_sqs_policy_resource" {
  default = []
}

variable "sqs_access_enabled" {
  default = false
}

variable "s3_bucket_arns" {
  type    = list(string)
  default = ["*"]
}

variable "s3_read_objects" {
  default = false
}

variable "ecs_sns_policy_resource" {
  default = ["arn:aws:sns:*:*:*"]
}

variable "sns_access_enabled" {
  default = false
}

variable "kms_enabled" {
  default = false
}
variable "kms_key_arns" {
  type = list(string)
  default = []
}
variable "textract_enabled" {
  default = false
}
variable "send_email" {
  description = "Permission to send emails"
  default     = false
}

variable "user_pool_arn_enabled" {
  default = false
}

variable "user_pool_arn" {
  default = ""
}

variable "user_pool_name" {
  description = "Name for the Cognito User Pool (only used when user_pool_arn_enabled is true)"
  default = ""
}

variable "user_pool_password_policy" {
  description = "Password policy for the Cognito User Pool"
  type = object({
    minimum_length                   = number
    require_lowercase                = bool
    require_numbers                  = bool
    require_symbols                  = bool
    require_uppercase                = bool
    temporary_password_validity_days = number
  })
  default = {
    minimum_length                   = 8
    require_lowercase                = true
    require_numbers                  = true
    require_symbols                  = false
    require_uppercase                = true
    temporary_password_validity_days = 7
  }
}

variable "user_pool_client_name" {
  description = "Name for the Cognito User Pool Client (only used when user_pool_arn_enabled is true)"
  default = ""
}