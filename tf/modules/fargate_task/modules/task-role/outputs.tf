output "arn" {
  value = aws_iam_role.ecs_task_role.arn
}

output "task_role" {
  value = aws_iam_role.ecs_task_role
}

output "user_pool_id" {
  description = "ID of the Cognito User Pool (only available when user_pool_arn_enabled is true)"
  value       = var.user_pool_arn_enabled ? aws_cognito_user_pool.main[0].id : null
}

output "user_pool_arn" {
  description = "ARN of the Cognito User Pool (only available when user_pool_arn_enabled is true)"
  value       = var.user_pool_arn_enabled ? aws_cognito_user_pool.main[0].arn : null
}

output "user_pool_client_id" {
  description = "ID of the Cognito User Pool Client (only available when user_pool_arn_enabled is true)"
  value       = var.user_pool_arn_enabled ? aws_cognito_user_pool_client.main[0].id : null
}

output "user_pool_client_secret" {
  description = "Secret of the Cognito User Pool Client (only available when user_pool_arn_enabled is true)"
  value       = var.user_pool_arn_enabled ? aws_cognito_user_pool_client.main[0].client_secret : null
  sensitive   = true
}
