variable "ecr_repository_name" {
  description = "ECR repository to store our Docker images"
}

variable "prefix" {
  description = "Prefix to use on ALB, target group and service names"
}

variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "fargate_cpu" {
  description = "Fargate instance CPU units to provision (1 vCPU = 1024 CPU units)"
  default     = "256"
}

variable "fargate_memory" {
  description = "Fargate instance memory to provision (in MiB)"
  default     = "512"
}

variable "task_definition" {
  description = "path to task definition json"
}

variable "service_name" {
  description = "ECS service name"
}

variable "cluster_name" {
  description = "ECS cluster name"
}

variable "dynamo_access_enabled" {
  default     = false
  description = "Dynamo access"
}

variable "ecs_dynamo_policy_resource" {
  description = " ECS dynamo policy resources"
}

variable "ecs_lambda_policy_resource" {
  description = "ECS policy resources for lambda integration"
  default = []
}

variable "lambda_access_enabled" {
  default = false
}

variable "secrets_enabled" {
  default = false
}

variable "secrets_arns" {
  default = []
  type = list(string)
}

variable "ecs_sqs_policy_resource" {
  description = "ECS policy resources for sqs integration"
  default = []
}

variable "sqs_access_enabled" {
  default = false
}

variable "ecs_sns_policy_resource" {
  description = "ECS policy resources for sqs integration"
  default = []
}

variable "sns_access_enabled" {
  default = false
}

variable "s3_bucket_arns" {
  type = list(string)
  default = []
}
variable "s3_read_objects" {
  default = false
}

variable "environment" {
  description = "The environment"
}

variable "container_name" {
  description = "main app container name"
}

variable "tags" {
  description = "Tags to use on project components"
  type        = map
}

variable "app_port" {
  description = "Port exposed by the docker image to redirect traffic to"
}

variable "account_id" {
  type = string
}

variable "app_environments" {
  type = string
}

variable "other_container_definitions" {
  type = map(string)
  default = {}
}

variable "use_fargate" {
  default = true
}

variable "textract_enabled" {
  default = false
}

variable "send_email" {
  description = "Permission to send emails"
  default     = false
}

variable "user_pool_arn_enabled" {
  default = false
}

variable "user_pool_arn" {
  default = ""
}

variable "user_pool_name" {
  description = "Name for the Cognito User Pool (only used when user_pool_arn_enabled is true)"
  default = ""
}

variable "user_pool_password_policy" {
  description = "Password policy for the Cognito User Pool"
  type = object({
    minimum_length                   = number
    require_lowercase                = bool
    require_numbers                  = bool
    require_symbols                  = bool
    require_uppercase                = bool
    temporary_password_validity_days = number
  })
  default = {
    minimum_length                   = 8
    require_lowercase                = true
    require_numbers                  = true
    require_symbols                  = false
    require_uppercase                = true
    temporary_password_validity_days = 7
  }
}

variable "user_pool_client_name" {
  description = "Name for the Cognito User Pool Client (only used when user_pool_arn_enabled is true)"
  default = ""
}

variable "kms_enabled" {
  default = false
}

variable "kms_key_arns" {
  default = []
}

/*
  Usage: "ENVIRONMENT_VARIABLE_NAME" = "SECRET_VALUE"
  {
    "INTEGRATIONS_CLEARSALE_HOST" = "${aws_secretsmanager_secret.via1_clearsale-credentials.arn}:HOST::"
  }

  Check secrets_mapping.tf for more samples
*/
variable "secrets_map" {
  type = map(string)
  default = {}
}

variable "ephemeral_storage" {
  description = "Fargate ephemeral storage"
  default     = 0
}